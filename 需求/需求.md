# 自然语言指令解析与领域坐标系意图生成系统需求文档

## 项目背景

* 在构建现代业务系统的过程中，我们计划逐步从传统的「配置驱动」模式（配置 -> DSL）过渡到更智能的「意图驱动」模式（意图 -> DSL）。这种转变的核心在于通过理解用户的自然语言需求，更快速、准确地转换用户需求，从而满足复杂多变的业务场景。

* 当前的系统需要支持使用自然语言直接描述对数据模型的修改需求，例如"在客户表添加一个整数类型的字段'客户等级'，并删除它与订单表的关联"。为了实现这一点，我们计划将用户的自然语言指令解析为领域模型（领域坐标系）上的结构化意图表示，并据此更新领域模型配置。

* 为了支持这种意图驱动的转型，我们设计了**领域坐标系（DSL 领域模型）**作为系统的核心。领域坐标系本质上是一个多层次的树状结构，包含了系统中的对象、属性、结构、类型、规则等元数据。通过**状态型操作**（QUERY|ADD|UPDATE|DELETE）或**行为型操作**（EXECUTE|RUN|START|STOP等）与领域坐标系的组合，我们可以定义数百个原子意图，覆盖复杂的业务操作单元。

* 目前使用大模型通过提示词已经能够初步解析此类指令并生成相应的意图，这证明了模型对指令语义的理解可行性。但我们希望通过构建高质量的训练数据并进行小模型微调，来实现一个可离线部署的专用模型。该模型能将用户的自然语言需求准确转换为**领域意图**，并最终生成可执行的DSL代码，应用于领域坐标系的配置更新。

## 项目目标

* **自然语言解析与DSL生成**：能够将用户给出的自然语言修改指令解析为领域坐标系的操作意图集合（JSON格式结构化数据），并进一步生成可执行的DSL代码。通过业务组件与DSL领域坐标系的映射关系，实现从自然语言到具体业务操作的完整转换链路。
* **训练数据构建**：构建高质量、成规模的训练数据集，数据集应覆盖状态型操作和行为型操作的各种组合场景，其中包含各种自然语言表述及其对应的领域意图结构，用于模型训练和蒸馏。
* **模型微调**：基于所生成的数据集，采用 LoRA 等微调技术对现有的大模型（如 Qwen-3 4B 或 Qwen-2.5 7B-Instruct）进行微调，使之在特定业务领域（如车辆租赁管理系统领域）上具备更精确的指令解析能力。
* **集成与扩展**：设计模型的集成方案，使其能够用于实际系统中。当用户发出自然语言指令时，系统能够识别业务领域，并调用相应的微调模型（Adapter）进行解析，输出预期的领域意图结果。
* **可扩展性**：系统应能够扩展到多个业务领域（通过门控机制选择对应领域的模型），支持不同业务组件的DSL领域坐标系映射，实现多领域的意图驱动开发模式。

## 名词解释

为了便于理解，下文中的关键名词定义如下：

* **DSL（Domain Specific Language）**：领域特定语言，是一种专门针对特定业务领域设计的计算机语言。在本系统中，DSL用于描述和执行业务操作，是意图驱动模式的最终输出形态。
* **领域坐标系（DSL 领域模型）**：指系统的领域模型定义，本质上是一个多层次的树状结构，包括对象、属性、结构、类型、规则等的元数据描述。它通常以JSON配置形式表示（参考"领域坐标系.json5"文档），而非直接的物理数据库结构。操作领域坐标系意味着修改这一模型配置，并能生成相应的可执行DSL代码。
* **配置驱动模式**：传统的系统构建方式，通过预定义的配置文件来生成DSL代码和业务逻辑。这种模式需要用户熟悉配置规则和语法。
* **意图驱动模式**：更智能的系统构建方式，通过理解用户的自然语言意图，直接生成对应的DSL代码。这种模式降低了用户的学习成本，提高了系统的易用性。
* **原子意图**：对领域模型进行的最小粒度操作单元。通过**状态型操作**（QUERY|ADD|UPDATE|DELETE）或**行为型操作**（EXECUTE|RUN|START|STOP等）与领域坐标系的组合，可以定义数百个原子意图，每个原子意图对应一个具体的业务操作。
* **业务组件**：系统中的基础构建模块，包括系统、表单、流程等。每个业务组件都会映射到一个或多个DSL领域坐标系，通过这种映射关系，可以将用户的自然语言意图精确地转换为具体的业务操作单元。
* **意图（Intent）**：指对领域模型进行的一个原子操作，通常包括操作类型（intentType）和目标对象等。例如，添加字段、删除关系、更新实体属性等都视为不同的意图类型。
* **训练样本**：由一条自然语言指令及其对应的意图列表组成的数据项。格式通常为 JSON 对象，包含 `"instruction"` 字段和 `"intent"` 字段，必要时还包括 `"label"` 字段用于标注特殊样本类型（如对抗样本、负样本）。
* **对抗样本**：指有意包含错误或非常规表述的指令，用于测试和提高模型的鲁棒性。例如，使用了一个不规范的数据类型名等。模型应能识别或纠正此类错误。
* **负样本**：指无法被正确解析的指令（通常由于信息不全或上下文缺失），用于让模型学习在无效输入时不输出错误结果。例如，只给出字段名但缺乏实体信息的指令。

## 功能需求

本节详细描述系统应实现的功能，包括自然语言指令到领域意图的转换流程，训练数据构造，以及模型微调与部署策略。

### 1. 自然语言指令解析流程

系统应能够**将用户输入的自然语言指令解析为领域意图的结构化表示**。具体包括以下步骤：

1. **领域变更规范输入**：首先，根据业务需求确定领域模型需要进行的修改。例如，本示例中有两个独立的修改场景：

    * 修改“客户”实体：增加一个名为“客户等级”的整型字段，移除“客户”实体与“订单”实体之间的一对多关系。
    * 修改“订单”实体：启用逻辑删除功能，并将对应表名更新为“Customer\_O2M\_Order”，另外增加两个字段：“订单金额”（DECIMAL 类型）和“租赁时长”（INT 类型）。

2. **构建初始指令-意图对**：针对每一条领域变更需求，生成一对“自然语言指令”和“领域意图”。

    * **自然语言指令**：将业务需求转述为口语化的单句或多句描述，符合日常说法。例如，对于上述第一个场景，可形成指令：“修改客户表，增加客户等级字段（类型为数字），并删除与订单表的关联。”
    * **领域意图**：用结构化JSON列出实现该指令所需的所有原子操作列表。每个操作包含：

        * `intentType`：操作类型，如 `"ADD_COLUMN"`（添加字段）、`"DELETE_RELATION"`（删除关系）、`"UPDATE_ENTITY"`（更新实体属性）等。
        * `targetConceptName`：操作作用的对象，例如实体名“客户”或“订单”。
        * `props`：操作的详细属性。比如添加字段需提供字段名称 `name` 和数据类型 `stdSqlType`；删除关系需指明关系的两端 `from` 和 `to` 实体等。

   **示例**：以下JSON展示了指令和意图的示例对，其中指令描述对“客户”实体的修改，意图则包含两个操作（添加字段和删除关系）：

   ```json
   {
     "instruction": "修改客户表，增加客户等级字段（类型为数字），并删除与订单表的关联。",
     "intent": [
       {
         "intentType": "ADD_COLUMN",
         "targetConceptName": "客户",
         "props": {
           "name": "客户等级",
           "stdSqlType": "INT"
         }
       },
       {
         "intentType": "DELETE_RELATION",
         "targetConceptName": "客户",
         "props": {
           "from": "客户",
           "to": "订单"
         }
       }
     ]
   }
   ```

   上述意图表示：在“客户”实体中添加一个名为“客户等级”的整型字段，并移除“客户”与“订单”之间的关系。

3. **数据增广（语义重述）**：系统需针对每条指令生成多种**语义等价**的自然语言表述，用于丰富训练样本。这一步可以借助预定义的同义词库或大语言模型（LLM）来实现，确保模型未来能理解各种说法。增广的要求：

    * **多样性**：句式和用词应有所不同，但语义必须与原指令一致，不引入或删减需求。比如“添加”可以替换为“新增”或“加上”，“删除关联”可表述为“取消关联”或“断开…关系”等。
    * **通顺性**：生成的语句必须符合日常语言习惯，避免生硬直译或语法错误的句子。
    * **有限冗余**：若某些生成语句语义重复度过高（仅细微字面差异）或结构混乱不通顺，则应剔除，保留表达清晰且有区分度的几种。

   **示例**：针对上述指令的可能增广表述：

    * “请在客户实体添加一个整型字段‘客户等级’，同时删除其与订单表的关联。”
    * “给客户表加一个叫‘客户等级’的数字字段，并取消它和订单之间的关系。”
    * “在客户表新增整数类型的‘客户等级’字段，然后断开客户和订单表之间的关系。”

   *（说明：以上仅举三例，实际应根据需要生成尽可能多样且有效的表述。相似度极高的如“加一个字段”和“添加一个字段”可以只保留一条。）*

4. **意图绑定与样本生成**：将通过增广筛选后的每条自然语言表述，与同一套领域意图进行绑定，生成多条训练样本。对于每一种有效表达，都构造形如步骤2示例的JSON对象，使`"instruction"`字段为该表述，`"intent"`字段则保持对应的意图列表不变。这样，一个需求场景将产生若干条指令不同但意图相同的训练样本，用于模型学习。

5. **对抗样本构造**：为提升模型鲁棒性，系统应生成一定数量的**对抗样本**。对抗样本是在原始需求基础上稍作**不合理或易错**的修改形成的指令，其对应意图在某些字段上体现这种异常，用特殊标签标识出来。常见对抗情况包括：

    * **错误的数据类型**：用户指令中使用了非标准类型名或错误类型。例如将应为“VARCHAR”的类型说成“字符串”或写成不规范的`"STRING"`。在意图上可以暂时保留用户字面描述（stdSqlType设为`"STRING"`），并记录此样本的标签为“类型错误”。
    * **模糊的字段/实体别名**：指令中使用了未经上下文定义的别名或简称，令模型难以判断作用对象。例如仅说“添加字段等级”而未指明是哪个实体的字段。

   **示例（对抗样本）**：
   指令：“添加字段‘等级’，类型为字符串。” – 用户使用了不标准的类型描述。
   对应意图：

   ```json
   [
     {
       "intentType": "ADD_COLUMN",
       "targetConceptName": "客户",
       "props": {
         "name": "等级",
         "stdSqlType": "STRING"
       }
     }
   ]
   ```

   并将样本的`"label"`标注为“对抗样本-类型错误”。模型训练时应认识到`"STRING"`不是合法类型（正确应为VARCHAR），从而学会纠正或提示错误。

6. **负样本构造**：生成一些**负样本**来模拟不完整或无效的指令场景。负样本的特点是指令无法映射出任何有效意图，模型应学会对这种输入不产出结果或输出Null意图。常见情况包括：

    * **缺少上下文**：指令过于简略，无法确定操作对象。例如：“加一个字段叫castGrade。” 其中既未提及实体，也未说明类型，模型无法确定如何处理。
    * **超出范围**：指令提出的操作不在系统支持的意图范围内。

   **示例（负样本）**：
   指令：“加一个字段叫 castGrade。”（无上下文）
   对应意图应为空列表 `[]`，并给样本添加`"label": "负样本-字段别名缺少上下文"` 标记。模型训练时，这类样本教导模型在无法解析有意义意图时，应返回空结果或请求更多信息，而不是乱猜。

7. **样本格式与输出**：所有生成的训练样本应统一结构和格式，便于后续用于训练。每个样本包含：

    * `instruction`：字符串，自然语言指令。
    * `intent`：数组或列表，包含一个或多个意图对象（每个意图对象的结构如步骤2所述）。
    * `label`：（可选）字符串，如果是非正常样本则标注其类型，例如“对抗样本-类型错误”或“负样本-…”。对于正常样本，此字段可省略或留空。

   **输出**可以汇总为一个JSON Lines文件或JSON数组，包含所有样本对象。例如，正常样本不含label，对抗/负样本含label：

   ```
   {
     "instruction": "请在客户实体添加一个整型字段“客户等级”，同时删除其与订单表的关联。",
     "intent": [ ... 对应第一节中示例的两个意图 ... ]
   }
   {
     "instruction": "添加字段等级，类型为字符串。",
     "intent": [
       {
         "intentType": "ADD_COLUMN",
         "targetConceptName": "客户",
         "props": {
           "name": "等级",
           "stdSqlType": "STRING"
         }
       }
     ],
     "label": "对抗样本-类型错误"
   }
   {
     "instruction": "加一个字段叫 custGrade。",
     "intent": [],
     "label": "负样本-字段别名缺少上下文"
   }
   ```

   *（上例为说明格式，实际文件中每个样本一般为独立的JSON对象行。）*

8. **领域模型一致性**：生成意图时，需确保所有字段名、实体名和属性名与领域坐标系定义保持一致，不可凭空创造不存在的名称。例如：

    * 使用正确的`stdSqlType`值，例如整型用`"INT"`，字符串类型应使用标准的`"VARCHAR"`等。
    * `targetConceptName`应对应领域模型中实际存在的实体标识名（如“客户”、“订单”等），关系中的实体引用必须有效。
    * 对于启用逻辑删除等操作，对应的领域属性为`useLogicalDelete` (布尔值)，修改表名对应属性为`tableName`等，要确保意图的`props`里使用正确的键名和值类型。

### 2. 模型训练与微调

在获得充分的训练数据后，需要对选定的基础大模型进行微调，使其掌握本领域的指令解析。本项目考虑采用 **LoRA** (Low-Rank Adaptation) 技术对模型微调，以减少训练开销并保留通用能力。具体要求：

* **基础模型选择**：优先考虑开源的中文大模型，如 Qwen-3 (4B 参数) 或 Qwen-2.5 (7B 参数) 的指令微调版本，作为基础模型。【理由：这些模型体量适中，适合作为本地离线部署的基础。】
* **微调方法**：使用领域专属数据集通过 LoRA 进行训练。LoRA 可以快速微调出一个 **Adapter** 权重文件，使模型在保持原有知识的同时，新增对特定领域指令的理解能力。
* **训练集规模**：尽可能扩大训练数据覆盖面。对于本业务领域，预期生成成百上千条指令-意图样本（包含各种正例、对抗和负例）。确保模型见过多样化的措辞和各种边界情况。
* **训练过程**：开发人员需要编写或使用已有的脚本来进行微调训练，包括:

    * 数据集整理与加载（按照模型需要的格式，例如prompt格式或直接输入-输出对）。
    * 设置适当的超参数（学习率、epoch等）以防止过拟合。
    * 训练完成后，验证模型在样本上的表现，确保**指令->意图**解析准确率达到要求。
* **微调结果**：输出每个领域对应的 LoRA Adapter 权重文件，以及推理时需要的配置（例如融合LoRA所需的参数）。

### 3. 模型部署与集成

在完成模型微调后，需要将其集成到实际系统中，实现用户输入到意图输出的自动解析。考虑到未来可能有多个业务领域的指令解析需求，部署时应设计**可扩展的架构**：

* **单领域集成**：针对本管理系统领域，将微调后的模型部署为一个服务/模块。用户在前端以自然语言提交指令后，后端调用该模型（加载相应LoRA权重的基础模型实例）解析出领域意图JSON，返回给业务逻辑层。业务逻辑再根据该意图去更新领域模型配置（例如通过调用内部的领域坐标系配置接口，实现增删字段、修改属性等操作）。
* **多领域扩展**：如果未来接入其它领域（比如财务系统、仓库系统等）的指令解析，可采用**门控机制**进行扩展。门控机制指的是在用户请求进入时，先判别其所属的业务域，然后路由到对应领域的解析模型上：

    * 可以通过简单的关键词匹配、分类模型或规则来判断指令涉及的实体/概念属于哪个业务域（例如检测到“客户”“订单”关键字属于管理系统域）。
    * 相应地加载该域的LoRA Adapter到基础模型，或者直接调用对应微调模型的服务。
    * 从而输出该域范围内的意图结果。如果指令不属于已知任何域，则返回无法处理或调用默认处理。

* **性能与响应**：解析模型的推理需要在可接受的时间内完成。针对4B\~7B量级的模型，推理一次通常在秒级。通过LoRA适配后，模型大小增加有限，尽量确保在CPU或单GPU环境下也可满足实时交互需求。必要时可采用模型量化技术加速推理。
* **正确性校验**：集成阶段，还应加入对模型输出结果的校验机制。例如，对生成的意图列表，可以验证实体和字段是否存在于当前领域模型定义，以避免模型产生不存在的对象。若发现异常，可记录日志或反馈提示，由人工或预定规则处理。

## 非功能需求

* **准确性**：模型解析结果要准确反映用户指令意图，不能丢漏操作，也不要擅自增加多余操作。对于关键信息（如字段名、类型），必须严格一致。建立校验集评估模型准确率，目标准确率应达到较高水平（如>90%）。
* **鲁棒性**：通过对抗样本训练，模型应能应对用户可能的用词错误或非常规表述，表现出一定容错能力。例如，当用户使用不规范字段类型时，模型要么能自动纠正为标准类型，要么明确标识无法识别该类型，而不是产生错误结果。
* **可维护性**：代码结构清晰，数据和逻辑解耦。生成训练数据的脚本应易于扩展，以便新增新的意图类型或适配新的业务领域。领域坐标系的配置接口或文件应被模块化管理，方便维护和版本控制。
* **文档与参考**：开发交付时需提供完整的文档，包括：

    * 代码说明和使用指南（如何生成数据集，如何运行微调，如何部署模型）。
    * 训练数据样例和格式说明，便于日后扩充数据或调试模型。

## 实现建议

为了顺利实现上述功能，以下是一些技术方案建议，供开发时参考：

* **利用大模型辅助数据生成**：在训练数据构建阶段，可以编写提示词让大模型（如ChatGPT等）根据给定的领域意图自动生成多样的用户自然语言表述。这种**由结构生成文本**的方法确保指令和意图一一对应，同时通过引入大型模型的多样表达，丰富了语料。在提示词设计时，可引导模型产出多种说法并进行自洽性检查（Chain-of-Thought，让模型解释它的意图理解以提高准确度），相当于用大模型给数据打“软标签”。
* **LoRA 微调技术**：采用 LoRA 对模型微调，只需训练出规模较小的差分权重，使基础模型适应新任务。这样也方便日后针对不同领域训练不同的 Adapter，而不必为每个领域保存完整模型副本。
* **领域适配与门控**：将各业务领域的训练数据和微调过程解耦。建议为每个领域单独生成数据集并训练 Adapter。在应用部署时，通过一个轻量的分类器或规则引擎充当门控，选择加载相应的 Adapter。这种架构允许按需扩展新的领域支持，而不会干扰已有领域的模型效果。
* **持续优化**：部署后收集实际用户使用数据（指令-意图对，以及模型未正确解析的案例），定期扩充训练集并微调模型，以不断提升系统准确度和覆盖面。尤其注意真实用户可能使用的新词汇或表达习惯，并将其纳入训练。

## 验收标准

项目完成后，应满足以下验收条件：

1. **功能完整**：能够解析预期范围内的自然语言指令，并输出正确的领域意图 JSON。以提供的测试用例验证，例如输入若干条预先设定的指令（包括正常指令和对抗/负样本），模型输出与预期结果一致。
2. **数据健全**：交付完整的训练数据集（或数据生成脚本）以及微调所用超参数配置，数据格式符合约定，无明显错误或遗漏。
3. **模型效果**：微调模型在测试集上解析准确率达到约定指标；对抗样本上有合理表现（如识别类型错误，给出空意图等）。同时在人机测试中，模型对未见过的同类指令也能正确泛化。
4. **文档齐备**：提供用户使用说明（如何部署和调用模型API）、开发文档和注释（方便代码维护和二次开发），以及领域模型配置修改的说明（确保他人能根据意图正确修改领域坐标系配置）。
5. **扩展性验证**：通过检查代码与架构，确认新增一个新领域时所需的工作量可控（例如只需增加新数据和Adapter，而核心代码无需大改）。这一点保证了项目后续拓展的可行性。