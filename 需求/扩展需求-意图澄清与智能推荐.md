# 意图澄清与智能推荐系统实现方案

## 1. 系统概述

基于现有的自然语言指令解析系统，本方案旨在构建一个意图澄清与智能推荐系统，通过分析用户输入的模糊或不完整需求，主动提出澄清问题并提供智能推荐，帮助用户更准确地表达其ORM模型设计意图。

### 1.1 核心目标

- **意图澄清**：当用户输入的自然语言指令存在歧义、不完整或缺乏上下文时，系统能够识别关键信息缺失点，并生成针对性的澄清问题
- **智能推荐**：基于业务域的微调权重，为用户提供相关的实体、关系、字段等推荐建议
- **渐进式引导**：引导用户完善需求描述，最终生成完整的领域意图

### 1.2 系统架构

```
用户输入 → 意图分析器 → 澄清问题生成器 → 智能推荐引擎 → 输出澄清问题+推荐
    ↓
用户回答 → 上下文更新器 → 意图完整性检查 → 领域意图生成器 → 最终意图输出
```

## 2. 核心组件设计

### 2.1 意图分析器

**功能职责**：
- 解析用户输入的自然语言，识别其意图类型和完整性
- 判断输入是否属于三个核心场景之一：整体模型设计、具体结构操作、新实体定义
- 提取已明确的信息要素（实体名、操作类型、属性等）
- 识别缺失的关键信息点

**实现策略**：
- 基于关键词匹配或语义分析，将用户输入映射到预定义的场景类型
- 使用实体识别技术提取已知的业务对象和操作类型
- 通过分析识别信息缺失，生成缺失信息列表

**输入示例**：
```
"我想设计一个车辆管理系统"
"给客户表加个字段"
"需要统计订单数据"
```

**输出格式**：
```json
{
  "scenario": "整体模型设计/具体数据操作/新实体定义",
  "extractedEntities": ["客户", "订单"],
  "extractedOperations": ["添加字段", "统计"],
  "missingInfo": ["目标实体", "字段类型", "业务目标"],
  "confidenceScore": 0.85
}
```

### 2.2 澄清问题生成器

**功能职责**：
- 基于意图分析结果，生成针对性的澄清问题
- 根据领域 Schema 和用户输入的上下文，动态调整问题内容
- 确保问题能够有效引导用户补充关键信息

**实现策略**：
。。。

**场景1问题示例**（整体模型设计）：
```json
{
  "questions": [
    {
      "item": "您计划设计的这个'{system_name}'，其核心业务目标或主要用户场景是什么？",
      "priority": 1
    },
    {
      "item": "这个{system_name}需要管理和追踪哪些最核心的业务对象或概念？",
      "priority": 2
    },
    {
      "item": "您希望在这个{entity_name}中包含哪些关键的属性或字段？",
      "priority": 3
    }
  ]
}
```

### 2.3 通用智能推荐引擎

**功能职责**：
- 支持多业务域（如 ORM、Form、API、流程等）的智能推荐
- 根据用户输入和上下文，推荐相关的对象、属性、结构、类型、规则等
- 提供基于权重配置和领域知识的高质量建议
- 具备良好的可扩展性，便于新增业务域

**推荐对象类型（可扩展）**：
- ORM：实体、关系、字段、数据类型、约束
- Form：表单控件类型、校验规则、布局方案、字段分组
- API：接口路径、参数、返回结构、鉴权方式
- 其它：可根据业务扩展

**数据来源**：
- 各业务域权重配置（如 orm_weights.yaml、form_weights.yaml）
- 各业务域元数据（如 orm_metadata.yaml、form_metadata.yaml）
- 领域知识库（常见模式、最佳实践、历史数据等）

**推荐策略（通用框架）**：

1. **对象推荐**
   - 基于权重分布和上下文相关性，推荐最有可能被用户需要的对象（如实体、控件、接口等）
   - 排除通用基础对象（如系统内置字段、通用控件等）

2. **属性/字段推荐**
   - 根据对象类型和业务域，推荐常用属性、字段或控件属性
   - 结合字段名语义、历史数据、领域特征

3. **关系/结构推荐**
   - 推荐对象间常见的结构关系（如实体间关系、表单分组、API 依赖等）

4. **类型/规则推荐**
   - 推荐合适的数据类型、控件类型、校验规则等

**推荐输出格式（通用）**：
```json
{
  "objectRecommendations": [
    {
      "name": "客户",
      "type": "实体/控件/接口/...",
      "confidence": 0.9,
      "reason": "基于xxx领域的高权重对象"
    }
  ],
  "relationRecommendations": [
    {
      "type": "一对多/父子对象/依赖/...",
      "from": "客户",
      "to": "订单",
      "confidence": 0.85
    }
  ],
  "propertyRecommendations": [
    {
      "object": "客户",
      "property": "客户等级",
      "suggestedType": "INT/下拉框/必填/...",
      "confidence": 0.8
    }
  ]
}
```

**推荐算法通用公式**：
- 对象推荐分数 = 基础权重 × 业务域匹配度 × 上下文相关性
- 关系推荐分数 = 关系权重 × 对象间匹配度 × 业务逻辑合理性
- 属性推荐分数 = 属性权重 × 语义匹配 × 领域相关性

**扩展性设计**：
- 新业务域只需补充权重配置和元数据，无需修改推荐引擎核心逻辑
- 推荐引擎通过“推荐对象类型”适配不同业务域

---

#### 业务域扩展示例：ORM 推荐策略

（以下为 ORM 领域的具体推荐策略示例，供其它业务域参考扩展）

**数据来源**：
- **orm_weights.yaml**：提供各业务域对象的权重分布，用于推荐优先级排序
- **orm_metadata.yaml**：提供标准的数据类型、约束类型等选项
- **业务域微调权重**：存储特定领域的常见实体关系模式

**推荐策略**：

1. **实体推荐**：
   - 基于权重配置中的entity权重分布
   - 考虑业务域特征（如车辆管理系统推荐：车辆、客户、租赁订单等）
   - 排除基础系统模块（用户、角色、权限等）

2. **关系推荐**：
   - 基于实体间的常见关系模式
   - 参考权重配置中的relation权重
   - 提供关系类型建议（一对一、一对多、多对多）

3. **字段推荐**：
   - 基于orm_metadata.yaml中的stdSqlType选项
   - 根据字段名称语义推断合适的数据类型
   - 考虑业务域特征（如客户表的客户等级、订单表的订单状态等）

**推荐输出格式**：
```json
{
  "entityRecommendations": [
    {
      "name": "车辆",
      "confidence": 0.9,
      "reason": "基于车辆管理系统域的高权重实体"
    }
  ],
  "relationRecommendations": [
    {
      "type": "一对多",
      "from": "客户",
      "to": "租赁订单",
      "confidence": 0.85
    }
  ],
  "typeRecommendations": [
    {
      "fieldName": "客户等级",
      "suggestedType": "INT",
      "confidence": 0.8
    }
  ]
}
```

## 3. 业务域微调权重的应用

### 3.1 权重数据解析

基于orm_weights.yaml文件，系统需要解析以下权重信息：

1. **全局权重分布**：
   - base_object_hierarchy (基础对象层级)
   - semantic_matrix (语义矩阵)
   - expression_matrix (表达矩阵)
   - robustness_matrix (鲁棒性矩阵)

2. **实体级权重**：
   - entity权重：70%，表明实体是核心关注点
   - domain权重：30%，数据类型域的重要性
   - 各字段的权重分布（name、displayName、tableName等）


### 3.2 推荐算法设计

**实体推荐算法**：
```
实体推荐分数 = 基础权重 × 业务域匹配度 × 上下文相关性
```

**关系推荐算法**：
```
关系推荐分数 = 关系权重 × 实体间匹配度 × 业务逻辑合理性
```

**字段推荐算法**：
```
字段推荐分数 = 字段权重 × 字段名语义匹配 × 业务域相关性
```

## 4. 对话流程设计

### 4.1 对话状态机

```
初始状态 → 意图识别 → 场景分类 → 问题生成 → 等待用户回答
    ↓
用户回答 → 信息提取 → 上下文更新 → 完整性检查
    ↓
[不完整] → 继续提问 → 等待用户回答
[完整] → 意图生成 → 结束对话
```

### 4.2 问题优先级策略

1. **宏观意图优先**：先澄清ORM层面的整体目标
2. **核心实体识别**：确定主要的业务对象
3. **关系梳理**：明确实体间的关联关系
4. **细节补充**：最后处理具体的字段和约束

### 4.3 智能问题选择

基于以下因素选择最优问题：
- **信息缺失的关键程度**：优先澄清对模型结构影响最大的信息
- **用户输入的具体程度**：根据用户表达的详细程度调整问题粒度
- **业务域特征**：结合特定业务域的常见模式选择问题
- **对话历史**：避免重复询问已确认的信息


## 5. 质量保证与评估

## 6. 总结

此实现方案通过构建意图澄清与智能推荐系统，能够有效解决用户输入模糊或不完整时的需求理解问题。系统基于业务域的微调权重提供智能推荐，通过引导用户完善需求描述，最终生成准确的领域意图。

关键特性包括：
- **智能场景识别**：准确识别用户意图所属的场景类型
- **针对性问题生成**：基于场景和上下文生成最相关的澄清问题
- **权重驱动推荐**：利用业务域微调权重提供高质量推荐
- **可扩展架构**：支持新业务域和新场景的扩展