global:
  generated.total_number: 10000

## 基础对象层级
base_object_hierarchy:
  weight: 0
  domain:
    weight: 30
    name:
      weight: 18.75
      create:
        weight: 57
      read:
        weight: 23
      update:
        weight: 0
      delete:
        weight: 20
    displayName:
      weight: 18.75
      create:
        weight: 33
      read:
        weight: 28
      update:
        weight: 39
      delete:
        weight: 0
    stdDomain:
      weight: 16.41
      create:
        weight: 33
      read:
        weight: 28
      update:
        weight: 39
      delete:
        weight: 0
    stdSqlType:
      weight: 16.41
      fixed_scope: [
          "VARCHAR",
          "CHAR",
          "TEXT",
          "BLOB",
          "BOOLEAN",
          "STRING",
          "INTEGER",
          "LONG",
          "FLOAT",
          "DOUBLE",
          "DATE",
          "TIME",
          "DATETIME",
          "TIMESTAMP"
      ]
      create:
        weight: 33
      read:
        weight: 28
      update:
        weight: 39
      delete:
        weight: 0
    precision:
      weight: 14.84
      create:
        weight: 33
      read:
        weight: 28
      update:
        weight: 39
      delete:
        weight: 0
    scale:
      weight: 14.84
      create:
        weight: 33
      read:
        weight: 28
      update:
        weight: 39
      delete:
        weight: 0

  entity:
    weight: 70
    min_count: 20
    name:
      weight: 10.72
      min_count: 10
      create:
        weight: 57
      read:
        weight: 23
      update:
        weight: 0
      delete:
        weight: 20
    displayName:
      weight: 10.71
      create:
        weight: 28
      read:
        weight: 28
      update:
        weight: 44
      delete:
        weight: 0
    tableName:
      weight: 10.71
      create:
        weight: 53
      read:
        weight: 47
      update:
        weight: 0
      delete:
        weight: 0
    useLogicalDelete:
      weight: 9.52
      fixed_scope: [true, false]
      create:
        weight: 33
      read:
        weight: 28
      update:
        weight: 39
      delete:
        weight: 0
    comment:
      weight: 8.93
      create:
        weight: 33
      read:
        weight: 28
      update:
        weight: 39
      delete:
        weight: 0
    tagSet:
      weight: 7.74
      create:
        weight: 30
      read:
        weight: 25
      update:
        weight: 25
      delete:
        weight: 20

    filter:
      weight: 5.36
      name:
        weight: 50
        create:
          weight: 50
        read:
          weight: 20
        update:
          weight: 0
        delete:
          weight: 30
      value:
        weight: 50
        create:
          weight: 30
        read:
          weight: 25
        update:
          weight: 25
        delete:
          weight: 20

    compute:
      weight: 4.17
      name:
        weight: 21.36
        create:
          weight: 57
        read:
          weight: 23
        update:
          weight: 0
        delete:
          weight: 20
      displayName:
        weight: 19.42
        create:
          weight: 28
        read:
          weight: 28
        update:
          weight: 44
        delete:
          weight: 0
      type:
        weight: 17.48
        create:
          weight: 33
        read:
          weight: 28
        update:
          weight: 39
        delete:
          weight: 0
      getterLogicalDesc:
        weight: 16.5
        create:
          weight: 33
        read:
          weight: 28
        update:
          weight: 39
        delete:
          weight: 0
      setterLogicalDesc:
        weight: 14.56
        create:
          weight: 33
        read:
          weight: 28
        update:
          weight: 39
        delete:
          weight: 0
      tagSet:
        weight: 10.68
        create:
          weight: 30
        read:
          weight: 25
        update:
          weight: 25
        delete:
          weight: 20

    column:
      weight: 9.52
      name:
        weight: 8.53
        create:
          weight: 57
        read:
          weight: 23
        update:
          weight: 0
        delete:
          weight: 20
      code:
        weight: 8.51
        create:
          weight: 33
        read:
          weight: 28
        update:
          weight: 39
        delete:
          weight: 0
      displayName:
        weight: 7.8
        create:
          weight: 28
        read:
          weight: 28
        update:
          weight: 44
        delete:
          weight: 0
      domain:
        weight: 7.8
        create:
          weight: 33
        read:
          weight: 28
        update:
          weight: 39
        delete:
          weight: 0
      stdSqlType:
        weight: 8.51
        fixed_scope: [
          "VARCHAR",
          "CHAR",
          "TEXT",
          "BLOB",
          "BOOLEAN",
          "STRING",
          "INTEGER",
          "LONG",
          "FLOAT",
          "DOUBLE",
          "DATE",
          "TIME",
          "DATETIME",
          "TIMESTAMP"
        ]
        create:
          weight: 53
        read:
          weight: 47
        update:
          weight: 0
        delete:
          weight: 0
      primary:
        weight: 8.51
        fixed_scope: [true, false]
        create:
          weight: 53
        read:
          weight: 47
        update:
          weight: 0
        delete:
          weight: 0
      mandatory:
        weight: 7.8
        fixed_scope: [true, false]
        create:
          weight: 33
        read:
          weight: 28
        update:
          weight: 39
        delete:
          weight: 0
      insertable:
        weight: 7.09
        fixed_scope: [true, false]
        create:
          weight: 33
        read:
          weight: 28
        update:
          weight: 39
        delete:
          weight: 0
      updatable:
        weight: 7.09
        fixed_scope: [true, false]
        create:
          weight: 33
        read:
          weight: 28
        update:
          weight: 39
        delete:
          weight: 0
      defaultValue:
        weight: 6.38
        create:
          weight: 33
        read:
          weight: 28
        update:
          weight: 39
        delete:
          weight: 0
      precision:
        weight: 6.38
        create:
          weight: 53
        read:
          weight: 47
        update:
          weight: 0
        delete:
          weight: 0
      scale:
        weight: 6.38
        create:
          weight: 33
        read:
          weight: 28
        update:
          weight: 39
        delete:
          weight: 0
      comment:
        weight: 4.96
        create:
          weight: 33
        read:
          weight: 28
        update:
          weight: 39
        delete:
          weight: 0
      tagSet:
        weight: 4.26
        create:
          weight: 30
        read:
          weight: 25
        update:
          weight: 25
        delete:
          weight: 20

    relation:
      weight: 9.52
      toOne:
        name:
          weight: 16.91
          create:
            weight: 55
          read:
            weight: 25
          update:
            weight: 0
          delete:
            weight: 20
        displayName:
          weight: 15.49
          create:
            weight: 28
          read:
            weight: 28
          update:
            weight: 44
          delete:
            weight: 0
        queryable:
          weight: 14.08
          fixed_scope: [true, false]
          create:
            weight: 33
          read:
            weight: 28
          update:
            weight: 39
          delete:
            weight: 0
        cascadeDelete:
          weight: 14.08
          create:
            weight: 33
          read:
            weight: 28
          update:
            weight: 39
          delete:
            weight: 0
        constraint:
          weight: 12.68
          create:
            weight: 30
          read:
            weight: 25
          update:
            weight: 25
          delete:
            weight: 20
        maxBatchLoadSize:
          weight: 8.45
          create:
            weight: 33
          read:
            weight: 28
          update:
            weight: 39
          delete:
            weight: 0
        comment:
          weight: 9.86
          create:
            weight: 33
          read:
            weight: 28
          update:
            weight: 39
          delete:
            weight: 0
        tagSet:
          weight: 8.45
          create:
            weight: 30
          read:
            weight: 25
          update:
            weight: 25
          delete:
            weight: 20
        join:
          leftProp:
            weight: 25
            create:
              weight: 33
            read:
              weight: 28
            update:
              weight: 39
            delete:
              weight: 0
          rightProp:
            weight: 25
            create:
              weight: 33
            read:
              weight: 28
            update:
              weight: 39
            delete:
              weight: 0
          leftValue:
            weight: 25
            create:
              weight: 33
            read:
              weight: 28
            update:
              weight: 39
            delete:
              weight: 0
          rightValue:
            weight: 25
            create:
              weight: 33
            read:
              weight: 28
            update:
              weight: 39
            delete:
              weight: 0
      toMany:
        name:
          weight: 17.65
          create:
            weight: 55
          read:
            weight: 25
          update:
            weight: 0
          delete:
            weight: 20
        displayName:
          weight: 16.18
          create:
            weight: 28
          read:
            weight: 28
          update:
            weight: 44
          delete:
            weight: 0
        queryable:
          weight: 10.29
          fixed_scope: [true, false]
          create:
            weight: 33
          read:
            weight: 28
          update:
            weight: 39
          delete:
            weight: 0
        cascadeDelete:
          weight: 14.71
          create:
            weight: 33
          read:
            weight: 28
          update:
            weight: 39
          delete:
            weight: 0
        maxBatchLoadSize:
          weight: 8.82
          create:
            weight: 33
          read:
            weight: 28
          update:
            weight: 39
          delete:
            weight: 0
        maxSize:
          weight: 13.24
          create:
            weight: 33
          read:
            weight: 28
          update:
            weight: 39
          delete:
            weight: 0
        comment:
          weight: 10.29
          create:
            weight: 33
          read:
            weight: 28
          update:
            weight: 39
          delete:
            weight: 0
        tagSet:
          weight: 8.82
          create:
            weight: 30
          read:
            weight: 25
          update:
            weight: 25
          delete:
            weight: 20
        join:
          leftProp:
            weight: 25
            create:
              weight: 33
            read:
              weight: 28
            update:
              weight: 39
            delete:
              weight: 0
          rightProp:
            weight: 25
            create:
              weight: 33
            read:
              weight: 28
            update:
              weight: 39
            delete:
              weight: 0
          leftValue:
            weight: 25
            create:
              weight: 33
            read:
              weight: 28
            update:
              weight: 39
            delete:
              weight: 0
          rightValue:
            weight: 25
            create:
              weight: 33
            read:
              weight: 28
            update:
              weight: 39
            delete:
              weight: 0
    uniqueKey:
      weight: 6.55
      name:
        weight: 21.52
        create:
          weight: 55
        read:
          weight: 25
        update:
          weight: 0
        delete:
          weight: 20
      displayName:
        weight: 19.35
        create:
          weight: 28
        read:
          weight: 28
        update:
          weight: 44
        delete:
          weight: 0
      columns:
        weight: 18.28
        create:
          weight: 33
        read:
          weight: 28
        update:
          weight: 39
        delete:
          weight: 0
      constraint:
        weight: 17.2
        create:
          weight: 42.86
        read:
          weight: 28.57
        update:
          weight: 0.0
        delete:
          weight: 28.57
      comment:
        weight: 12.9
        create:
          weight: 33
        read:
          weight: 28
        update:
          weight: 39
        delete:
          weight: 0
      tagSet:
        weight: 10.75
        create:
          weight: 30
        read:
          weight: 25
        update:
          weight: 25
        delete:
          weight: 20
    index:
      weight: 6.55
      name:
        weight: 21.52
        create:
          weight: 55
        read:
          weight: 25
        update:
          weight: 0
        delete:
          weight: 20
      displayName:
        weight: 19.35
        create:
          weight: 28
        read:
          weight: 28
        update:
          weight: 44
        delete:
          weight: 0
      unique:
        weight: 18.28
        fixed_scope: [true, false]
        create:
          weight: 33
        read:
          weight: 28
        update:
          weight: 39
        delete:
          weight: 0
      indexType:
        weight: 17.2
        fixed_scope: ["BTREE", "UNIQUE", "PRIMARY", "COMPOSITE", "FULLTEXT"]
        create:
          weight: 33
        read:
          weight: 28
        update:
          weight: 39
        delete:
          weight: 0
      comment:
        weight: 12.9
        create:
          weight: 33
        read:
          weight: 28
        update:
          weight: 39
        delete:
          weight: 0
      tagSet:
        weight: 10.75
        create:
          weight: 30
        read:
          weight: 25
        update:
          weight: 25
        delete:
          weight: 20
      columns:
        name:
          weight: 50
          create:
            weight: 45
          read:
            weight: 25
          update:
            weight: 0
          delete:
            weight: 30
        desc:
          weight: 50
          create:
            weight: 33
          read:
            weight: 28
          update:
            weight: 39
          delete:
            weight: 0

semantic_matrix:
  weight: 60
  intent_strategy:
    weight: 100
    atomic:
      weight: 35
    composite:
      weight: 30
    sequence:
      weight: 20
    implicit:
      weight: 15

expression_matrix:
  weight: 30
  lexical_layer:
    weight: 45
    synonym_replacement:
      weight: 35
    abbreviation:
      weight: 25
    colloquial_formal:
      weight: 10
    industry_jargon:
      weight: 20
    tone_particles:
      weight: 10
  syntactic_layer:
    weight: 30
    active_passive:
      weight: 35
    inversion:
      weight: 25
    sentence_split_merge:
      weight: 20
    punctuation_rearrange:
      weight: 5
    question_imperative:
      weight: 15
  discourse_layer:
    weight: 15
    multi_role:
      weight: 60
    coreference_resolution:
      weight: 40
  multilingual_layer:
    weight: 10
    chinese_english_mixed:
      weight: 60
    full_chinese:
      weight: 30
    full_english:
      weight: 8
    dialect:
      weight: 2

robustness_matrix:
  weight: 10
  noise_layer:
    weight: 40
    character_level:
      weight: 60
      spelling_input_errors:
        weight: 50
      format_symbol_mixed:
        weight: 30
      case_insensitive:
        weight: 20
    semantic_level:
      weight: 40
      self_correction_hesitation:
        weight: 35
      information_redundancy:
        weight: 25
      irrelevant_interference:
        weight: 20
      imprecise_terminology:
        weight: 15
      ambiguous_omission:
        weight: 5
  adversarial_layer:
    weight: 35
    schema_confusion:
      weight: 40
      field_entity_confusion:
        weight: 30
      relation_type_misplacement:
        weight: 25
      constraint_conflict:
        weight: 25
      type_mismatch:
        weight: 20
    intent_ambiguity:
      weight: 35
      unclear_operation_object:
        weight: 40
      ambiguous_operation_type:
        weight: 35
      undefined_parameter_range:
        weight: 25
    context_dependency:
      weight: 25
      distant_reference:
        weight: 50
      nested_instructions:
        weight: 30
      conditional_branch_confusion:
        weight: 20
  negative_samples_layer:
    weight: 25
    syntax_errors:
      weight: 35
      illegal_field_names:
        weight: 40
      unsupported_data_types:
        weight: 35
      invalid_constraint_combinations:
        weight: 25
    business_logic_errors:
      weight: 40
      circular_dependencies:
        weight: 35
      unreasonable_relationships:
        weight: 35
      data_integrity_violations:
        weight: 30
    impossible_operations:
      weight: 25
      physical_constraint_violations:
        weight: 50
      system_limit_conflicts:
        weight: 30
      temporal_logic_errors:
        weight: 20
