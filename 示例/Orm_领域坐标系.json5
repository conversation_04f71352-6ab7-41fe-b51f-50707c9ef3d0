//合法意图 = {QUERY|ADD|UPDATE|DELETE}_领域坐标系
{
  "orm": { // ORM 根对象，表示一个对象关系模型定义
    "displayName": "string", // 模型的展示名称
    "description": "string", // 模型的描述信息
    "domain": { // 数据类型域定义
      "name": "string", // 域名称（唯一标识）
      "displayName": "string", // 域的展示名称
      "stdDomain": "enum", // 标准域类型（如 file、json 等）
      "stdSqlType": "enum", // 标准 SQL 类型（如 VARCHAR、INT 等）
      "precision": "number", // 精度（适用于小数或定长类型）
      "scale": "number" // 小数位数（仅对小数字段适用）
    },
    "entity": { // 实体定义
      "name": "string", // 实体的唯一标识名
      "displayName": "string", // 实体的展示名称
      "useLogicalDelete": "boolean", // 是否使用逻辑删除
      "tableName": "string", // 对应数据库中的表名
      "tagSet": "set", // 标签集合，用于扩展用途
      "comment": "string", // 实体的注释信息
      "filter":{ // 实体级别的默认过滤条件
        "name": "string", // 过滤器名称
        "value": "string" // 过滤器值
      },
      "column": { // 字段定义
        "name": "string", // 字段名称（唯一标识）
        "code": "string", // 字段编码（可能用于代码生成）
        "displayName": "string", // 字段的展示名称
        "insertable": "boolean", // 是否允许插入时赋值
        "updatable": "boolean", // 是否允许更新时赋值
        "domain": "string", // 所属数据域
        "stdSqlType": "enum", // 对应的标准 SQL 数据类型
        "scale": "number", // 小数位数
        "precision": "number", // 字段精度（适用于数值型字段）
        "primary": "boolean", // 是否为主键字段
        "defaultValue": "string", // 默认值
        "comment": "string", // 字段注释
        "tagSet": "set", // 标签集合
        "mandatory": "boolean" // 是否为必填字段（非空）
      },
      "compute": { // 行计算字段定义
        "name": "string", // 字段名称
        "displayName": "string", // 展示名称
        "type": "string", // 数据类型
        "tagSet": "set", // 标签集合
        "getterLogicalDesc": "string", // 获取行计算值的逻辑描述
        "setterLogicalDesc": "string", // 设置行计算值的逻辑描述
      },
      "relation": { // 实体关系定义
        "toOne": { // 多对一关系定义
          "name": "string", // 关系名称
          "displayName": "string", // 展示名称
          "maxBatchLoadSize": "number", // 批量加载最大数量
          "queryable": "boolean", // 是否可被查询
          "tagSet": "set", // 标签集合
          "constraint": "string", // 外键约束名称
          "cascadeDelete": "boolean", // 删除时是否级联删除目标对象
          "comment": "string", // 注释信息
          "join": { // 连接字段映射
            "leftProp": "string", // 当前实体的字段名
            "leftValue": "any", // 当前实体字段的值
            "rightProp": "string", // 目标实体的字段名
            "rightValue": "any" // 目标实体字段的值
          }
        },
        "toMany": { // 一对多关系定义
          "name": "string", // 关系名称
          "displayName": "string", // 展示名称
          "maxBatchLoadSize": "number", // 批量加载最大数量
          "maxSize": "number", // 最大集合大小
          "queryable": "boolean", // 是否支持查询
          "tagSet": "set", // 标签集合
          "cascadeDelete": "boolean", // 删除时是否级联目标集合
          "comment": "string", // 注释信息
          "join": { // 连接字段映射
            "leftProp": "string", // 当前实体的字段名
            "leftValue": "any", // 当前实体字段的值
            "rightProp": "string", // 目标实体的字段名
            "rightValue": "any" // 目标实体字段的值
          }
        }
      },
      "uniqueKey": { // 唯一约束定义
        "name": "string", // 唯一键名称
        "displayName": "string", // 展示名称
        "columns": "string", // 包含字段名（多个字段用逗号分隔）
        "constraint": "string", // 数据库中的唯一约束名
        "tagSet": "set", // 标签集合
        "comment": "string" // 注释信息
      },
      "index": { // 索引定义
        "name": "string", // 索引名称
        "displayName": "string", // 展示名称
        "unique": "boolean", // 是否为唯一索引
        "indexType": "enum", // 索引类型（如 BTREE、HASH 等）
        "comment": "string", // 索引说明
        "tagSet": "set", // 标签集合
        "columns": { // 索引包含字段
          "name": "string", // 字段名称
          "desc": "boolean" // 是否倒序排列（true 为倒序）
        }
      }
    }
  }
}